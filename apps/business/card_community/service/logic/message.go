package logic

import (
	"context"
	"encoding/json"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// ValidateMessageContent 验证消息内容
// 根据消息类型验证对应的内容是否符合要求
func ValidateMessageContent(req *define.SendMessageReq) error {
	switch req.MessageType {
	case enums.MessageTypeText:
		// 文本消息必须有内容
		if req.Content == "" {
			return define.CC500201Err
		}
	case enums.MessageTypeImage:
		// 图片消息必须有媒体文件
		if req.Media == nil {
			return define.CC500203Err.SetMsg("图片消息必须包含媒体文件")
		}
		if req.Media.Type != enums.MediaTypeImage {
			return define.CC500203Err.SetMsg("媒体文件类型必须为图片")
		}
	case enums.MessageTypePost:
		// 帖子消息必须有帖子ID和快照
		if req.PostID == "" {
			return define.CC500207Err.SetMsg("帖子消息必须包含帖子ID")
		}
		if req.PostSnapshot == nil {
			return define.CC500207Err.SetMsg("帖子消息必须包含帖子快照")
		}
	}
	return nil
}

// GenerateMessageSummary 生成消息摘要
// 根据消息类型和内容生成用于会话列表显示的摘要文本
func GenerateMessageSummary(content string, messageType enums.MessageType) string {
	switch messageType {
	case enums.MessageTypeText:
		// 文本消息直接返回内容，如果太长则截取
		if len(content) > 50 {
			return content[:50] + "..."
		}
		return content
	case enums.MessageTypeImage:
		return "[图片]"
	case enums.MessageTypePost:
		return "新消息！"
	default:
		return "[消息]"
	}
}

// UpdateConversationLastMessage 更新会话的最后消息信息
// 同时更新发送者和接收者的会话记录，包括最后消息内容和未读数
// 同时处理消息发送状态（只有非智能回复才设置状态）
// 当消息类型为帖子快照时，同时更新会话的帖子ID
func UpdateConversationLastMessage(ctx context.Context, tx *gorm.DB, senderID, receiverID, messageID, content string, messageType enums.MessageType, isAutoReply bool, postID string) error {
	// 生成消息摘要
	summary := GenerateMessageSummary(content, messageType)

	now := time.Now()
	updateData := map[string]interface{}{
		"last_message_id":      messageID,
		"last_message_content": summary,
		"last_message_time":    &now,
		"last_message_type":    int32(messageType),
		"updated_at":           now,
	}

	// 如果是帖子消息类型且提供了帖子ID，则更新会话的帖子ID
	if messageType == enums.MessageTypePost && postID != "" {
		updateData["post_id"] = postID
	}

	// 更新发送者的会话记录
	senderUpdateResult := tx.Model(&model.Conversation{}).
		Where("participant_id = ? AND other_participant_id = ?", senderID, receiverID).
		Updates(updateData)
	if senderUpdateResult.Error != nil {
		log.Ctx(ctx).Errorf("更新发送者会话记录失败: %v", senderUpdateResult.Error)
		return commondefine.CommonErr
	}
	if senderUpdateResult.RowsAffected == 0 {
		log.Ctx(ctx).Errorf("发送者会话记录不存在或未更新")
		return commondefine.CommonErr
	}

	// 更新接收者的会话记录（增加未读数，非智能回复时设置状态）
	receiverUpdateData := map[string]interface{}{
		"last_message_id":      messageID,
		"last_message_content": summary,
		"last_message_time":    &now,
		"last_message_type":    int32(messageType),
		"unread_count":         gorm.Expr("unread_count + 1"),
		"updated_at":           now,
	}

	// 如果是帖子消息类型且提供了帖子ID，则更新会话的帖子ID
	if messageType == enums.MessageTypePost && postID != "" {
		receiverUpdateData["post_id"] = postID
	}

	// 只有非智能回复时才设置接收者状态为正常
	if !isAutoReply {
		receiverUpdateData["status"] = enums.StatusNormal.Int32() // 接收者收到消息后设置为正常
	}

	// 更新接收者会话记录
	receiverUpdateResult := tx.Model(&model.Conversation{}).
		Where("participant_id = ? AND other_participant_id = ?", receiverID, senderID).
		Updates(receiverUpdateData)
	if receiverUpdateResult.Error != nil {
		log.Ctx(ctx).Errorf("更新接收者会话记录失败: %v", receiverUpdateResult.Error)
		return commondefine.CommonErr
	}
	if receiverUpdateResult.RowsAffected == 0 {
		log.Ctx(ctx).Errorf("接收者会话记录不存在或未更新")
		return commondefine.CommonErr
	}

	return nil
}

// CheckMessageIdempotent 检查消息幂等性（仅使用缓存）
// 返回值：existingMessage, error
func CheckMessageIdempotent(ctx context.Context, clientMsgID string) (*define.SendMessageResp, error) {
	// 检查缓存
	cacheKey := constant.GetMessageIdempotentKey(clientMsgID)
	cachedData, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err != nil {
		// 缓存未命中，当作新消息处理
		return nil, nil
	}

	// 缓存命中，解析数据
	var cachedResp define.SendMessageResp
	if err := json.Unmarshal([]byte(cachedData), &cachedResp); err != nil {
		// 缓存数据解析失败，记录日志并当作新消息处理
		log.Ctx(ctx).Warnf("解析消息幂等性缓存失败: %v", err)
		return nil, nil
	}

	return &cachedResp, nil
}

// SetMessageIdempotentCache 设置消息幂等性缓存
func SetMessageIdempotentCache(ctx context.Context, clientMsgID string, resp *define.SendMessageResp) error {
	cacheKey := constant.GetMessageIdempotentKey(clientMsgID)
	jsonData, err := json.Marshal(resp)
	if err != nil {
		log.Ctx(ctx).Errorf("序列化消息幂等性缓存数据失败: %v", err)
		return err
	}

	// 同步设置缓存，避免并发问题
	if err := global.REDIS.Set(ctx, cacheKey, jsonData, constant.MessageIdempotentTTL).Err(); err != nil {
		log.Ctx(ctx).Errorf("设置消息幂等性缓存失败: %v", err)
		return err
	}

	return nil
}

// GetParticipantIDsByPostID 根据帖子ID获取参与者ID列表
func GetParticipantIDsByPostID(ctx context.Context, postID string) ([]string, error) {
	messageSchema := repo.GetQuery().Message
	messageQueryBuilder := search.NewQueryBuilder().
		Eq(messageSchema.PostID, postID)

	messages, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).SelectList(messageQueryBuilder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("根据帖子ID查询消息失败: %v", err)
		return nil, commondefine.CommonErr.Err(err)
	}

	if len(messages) == 0 {
		return []string{}, nil
	}

	// 收集参与者ID
	participantIDSet := make(map[string]bool)
	for _, msg := range messages {
		participantIDSet[msg.BigUserID] = true
		participantIDSet[msg.SmallUserID] = true
	}

	// 将参与者ID转换为切片
	var participantIDs []string
	for id := range participantIDSet {
		participantIDs = append(participantIDs, id)
	}

	return participantIDs, nil
}

// GetUserIDsByKeyword 根据关键字搜索消息内容获取用户ID列表
func GetUserIDsByKeyword(ctx context.Context, keyword string) ([]string, error) {
	messageSchema := repo.GetQuery().Message
	messageQueryBuilder := search.NewQueryBuilder().
		Like(messageSchema.Content, "%"+keyword+"%")

	messages, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).SelectList(messageQueryBuilder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("根据关键字搜索消息内容失败: %v", err)
		return nil, commondefine.CommonErr.Err(err)
	}

	if len(messages) == 0 {
		return []string{}, nil
	}

	// 收集消息中的用户ID
	userIDSet := make(map[string]bool)
	for _, msg := range messages {
		userIDSet[msg.BigUserID] = true
		userIDSet[msg.SmallUserID] = true
	}

	// 转换为切片
	var result []string
	for userID := range userIDSet {
		result = append(result, userID)
	}

	return result, nil
}

// GetConversationMessageCounts 批量统计会话的消息数量
func GetConversationMessageCounts(ctx context.Context, conversations []*model.Conversation) (map[string]int, error) {
	messageCountMap := make(map[string]int)

	for _, conv := range conversations {
		// 计算用户对的大小用户ID
		bigUserID, smallUserID, _ := model.CalculateUserPair(conv.ParticipantID, conv.OtherParticipantID)

		messageSchema := repo.GetQuery().Message
		count, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).Count(
			search.NewQueryBuilder().
				Eq(messageSchema.BigUserID, bigUserID).
				Eq(messageSchema.SmallUserID, smallUserID).
				Build(),
		)
		if err != nil {
			log.Ctx(ctx).Warnf("统计会话消息数量失败，会话ID: %s, 错误: %v", conv.ID, err)
			messageCountMap[conv.ID] = 0
		} else {
			messageCountMap[conv.ID] = int(count)
		}
	}

	return messageCountMap, nil
}

// CheckMessageFrequencyLimit 检查消息发送频率限制
// 如果会话处于频率限制状态，则统计发送者向接收者发送的连续消息数量
// 如果超过5条则返回错误
func CheckMessageFrequencyLimit(ctx context.Context, conversation *model.Conversation, senderID, receiverID string) error {
	// 如果会话没有限制标记，直接通过
	if !conversation.IsLimitedStatus() {
		return nil
	}

	// 统计发送者向接收者发送的连续消息数量（从最后一条对方发送的消息之后开始计算）
	count, err := countConsecutiveMessages(ctx, senderID, receiverID)
	if err != nil {
		log.Ctx(ctx).Errorf("统计连续消息数量失败: %v", err)
		return commondefine.CommonErr
	}

	// 如果已经发送了5条或更多消息，则阻止发送
	if count >= 5 {
		return define.CC500208Err
	}

	return nil
}

// countConsecutiveMessages 统计发送者向接收者发送的消息数量
func countConsecutiveMessages(ctx context.Context, senderID, receiverID string) (int64, error) {
	// 计算用户对的大小用户ID
	bigUserID, smallUserID, senderDirection := model.CalculateUserPair(senderID, receiverID)

	messageSchema := repo.GetQuery().Message

	// 统计发送者发送的所有消息
	queryBuilder := search.NewQueryBuilder().
		Eq(messageSchema.SmallUserID, smallUserID).
		Eq(messageSchema.BigUserID, bigUserID).
		Eq(messageSchema.Direction, senderDirection).
		Ne(messageSchema.MessageType)
	// Eq(messageSchema.IsSmartReply, false) // 排除智能回复

	// 统计消息数量
	count, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).Count(queryBuilder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("统计消息数量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// SetConversationStatus 设置会话的状态
func SetConversationStatus(ctx context.Context, tx *gorm.DB, conversationID string, limited bool) error {
	conversationSchema := repo.GetQuery().Conversation

	var status enums.Status
	if limited {
		status = enums.StatusLimited // 限制中
	} else {
		status = enums.StatusNormal // 正常
	}

	// 创建会话模型用于更新
	conversation := &model.Conversation{
		ID:     conversationID,
		Status: status.Int32(),
	}

	err := repo.NewConversationRepo(conversationSchema.WithContext(ctx)).UpdateById(conversation)
	if err != nil {
		log.Ctx(ctx).Errorf("更新会话状态失败: %v", err)
		return commondefine.CommonErr
	}

	return nil
}
